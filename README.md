# Api

## Description


## Installation

```bash
$ yarn install
```

```bash
$ cp .env.example .env
```
update .env configutation


## Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```


## Test

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## module create
```
nest g module modules/center
nest g controller modules/center --spec false
nest g service modules/center --spec false
mkdir modules/center/dto


```
