import { Inject, Injectable } from '@nestjs/common';
import { Asset } from './asset.entity';
import { BaseService } from '../../core/base/base.service';
import { User } from '../users/user.entity';
import { AssetDto } from './dto/asset.dto';

@Injectable()
export class AssetService extends BaseService<Asset, AssetDto> {
  constructor(@Inject('AssetRepository') repository: typeof Asset) {
    super(repository);
  }

  saveFile(file: Express.Multer.File, user: User) {
    return this.create({
      path: file.path,
      size: file.size,
      userId: user.id,
    });
  }

  async saveMultiFile(files: Express.Multer.File[], user: User) {
    const assets: Partial<AssetDto>[] = [];

    files.map((file) => {
      assets.push({
        path: file.path,
        size: file.size,
        userId: user.id,
      });
    });

    await this.repository.bulkCreate(assets);
    return assets;
  }
}
