import {
  Controller,
  HttpStatus,
  ParseFilePipeBuilder,
  Post,
  Request,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { AssetService } from './asset.service';
import { diskStorage } from 'multer';
import { editFileName } from './asset.util';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@ApiTags('asset')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('asset')
export class AssetController {
  constructor(protected readonly assetService: AssetService) {}

  @Post('upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: editFileName,
      }),
    })
  )
  uploadFile(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: 'jpeg|png|csv|pdf|excel',
        })
        .addMaxSizeValidator({
          maxSize: 1000000,
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        })
    )
    file: Express.Multer.File,
    @Request() request: any
  ) {
    return this.assetService.saveFile(file, request.user);
  }

  @Post('multiple')
  @UseInterceptors(
    FilesInterceptor('file', 20, {
      storage: diskStorage({
        destination: './uploads',
        filename: editFileName,
      }),
    })
  )
  async uploadMultipleFiles(
    @UploadedFiles() // new ParseFilePipeBuilder()
    //     fileType: 'jpeg|png|csv|pdf|excel',
    files //   .addFileTypeValidator({
    //   })
    //   .addMaxSizeValidator({
    //     maxSize: 1000000
    //   })
    //   .build({
    //     errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY
    //   }),
    : Express.Multer.File[],
    @Request() request: any
  ) {
    return this.assetService.saveMultiFile(files, request.user);
  }
}
