import {
  IsEmail,
  Is<PERSON>num,
  IsISO<PERSON><PERSON>,
  IsNot<PERSON>mpty,
  IsO<PERSON>al,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { Gender } from '../../../shared/enum/gender';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @ApiProperty()
  @IsString()
  @MinLength(6)
  readonly password: string;

  @ApiProperty()
  @IsString()
  readonly firstName: string;

  @ApiProperty()
  @IsString()
  readonly lastName: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(Gender)
  readonly gender: Gender;

  @ApiProperty()
  @IsOptional()
  @IsISO8601()
  readonly birthday: string;
}
