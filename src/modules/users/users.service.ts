import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { User } from './user.entity';
import { genSalt, hash } from 'bcrypt';
import { UserDto } from './dto/user.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UserLoginResponseDto } from '../auth/dto/user-login-response.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResetCodes } from './user-reset-code.entity';

@Injectable()
export class UsersService {
  constructor(
    @Inject('UsersRepository') private readonly usersRepository: typeof User,
    @Inject('UserResetCodesRepository')
    private readonly userResetRepository: typeof UserResetCodes
  ) {}

  async findAll() {
    const users = await this.usersRepository.findAll<User>();
    return users.map((user) => new UserDto(user));
  }

  async getUser(id: string) {
    const user = await this.usersRepository.findByPk<User>(id);
    if (!user) {
      throw new HttpException(
        'User with given id not found',
        HttpStatus.NOT_FOUND
      );
    }
    return new UserDto(user);
  }

  async findUser(id, options = {}) {
    return await this.usersRepository.findByPk<User>(id, {
      ...options,
    });
  }

  async saveResetCode(email: string, resetCode: number): Promise<void> {
    await UserResetCodes.destroy({
      where: { email },
    });
    const resetCodeEntry = new UserResetCodes();
    resetCodeEntry.email = email;
    resetCodeEntry.resetCode = resetCode;
    await resetCodeEntry.save();
  }

  async getUserByEmail(email: string) {
    return await this.usersRepository.findOne<User>({
      where: { email },
    });
  }

  async create(createUserDto: CreateUserDto) {
    try {
      const user = new User();
      user.email = createUserDto.email.trim().toLowerCase();
      user.firstName = createUserDto.firstName;
      user.lastName = createUserDto.lastName;
      user.gender = createUserDto.gender;
      user.birthday = createUserDto.birthday;

      const salt = await genSalt(10);
      user.password = await hash(createUserDto.password, salt);

      const userData = await user.save();

      // when registering then log user in automatically by returning a token
      // const token = await this.authService.signToken(userData);
      return new UserLoginResponseDto(userData /*token*/);
    } catch (err) {
      if (err.original.constraint === 'users_email_key') {
        throw err;
        // throw new ForbiddenException();

        // throw new BadRequestException(
        //   err.constructor.name,
        //   { cause: new Error(), description: 'Some error description' }
        // );

        // throw new HttpException(
        //   `User with email '${err.errors[0].value}' already exists`,
        //   HttpStatus.CONFLICT,
        //   { cause: new Error(), description: 'Some error description' }
        // );
      }

      throw new HttpException(err, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    const user = await this.usersRepository.findByPk<User>(id);
    if (!user) {
      throw new HttpException('User not found.', HttpStatus.NOT_FOUND);
    }

    user.firstName = updateUserDto.firstName || user.firstName;
    user.lastName = updateUserDto.lastName || user.lastName;
    user.gender = updateUserDto.gender || user.gender;
    user.birthday = updateUserDto.birthday || user.birthday;

    try {
      const data = await user.save();
      return new UserDto(data);
    } catch (err) {
      throw new HttpException(err, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async delete(id: string) {
    const user = await this.usersRepository.findByPk<User>(id);
    if (!user) {
      throw new HttpException('User not found.', HttpStatus.NOT_FOUND);
    }
    await user.destroy();
    return new UserDto(user);
  }

  async getUserBySetId(setId: string): Promise<UserResetCodes | null> {
    return UserResetCodes.findOne({ where: { setId } });
  }

  async resetPassword(setId: string, newPassword: string): Promise<void> {
    const userResetCode = await this.userResetRepository.findOne({
      where: {
        setId: setId,
      },
    });
    if (!userResetCode) {
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Güvenlik Kodu bulunamadı!',
          error: 'customError',
        },
        HttpStatus.BAD_REQUEST
      );
    }
    const user = await this.usersRepository.findOne({
      where: {
        email: userResetCode.email,
      },
    });
    if (!user) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Kullanıcı bulunamadı.',
          error: 'customError',
        },
        HttpStatus.NOT_FOUND
      );
    }
    const salt = await genSalt(10);
    user.password = await hash(newPassword, salt);
    try {
      await user.save();
      await userResetCode.destroy();
    } catch (err) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Şifre Sıfırlama Tamamlanamadı!',
          error: 'customError',
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getResetCodeByCode(
    resetCode: number,
    email: string
  ): Promise<UserResetCodes | null> {
    const currentDate = new Date();

    const resetCodeEntry = await UserResetCodes.findOne({
      where: {
        resetCode,
        email,
      },
    });

    if (resetCodeEntry) {
      const codeCreatedAt = resetCodeEntry.createdAt.getTime();
      if (codeCreatedAt + 30 * 60 * 1000 < currentDate.getTime()) {
        await resetCodeEntry.destroy();
        return null;
      }
    }

    return resetCodeEntry;
  }
}
