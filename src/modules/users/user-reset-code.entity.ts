import { Column, DataType, DeletedAt, Table, Unique, } from 'sequelize-typescript';
import { ResourceEntity } from '../../core/base/resource.entity';
import { BaseEntity } from 'src/core/base/base.entity';

@Table({
  tableName: 'user_reset_codes',
})
export class UserResetCodes extends BaseEntity<UserResetCodes> {
  @Column({type: DataType.UUID})
  setId: string;

  @Column
  email: string;

  @Column
  resetCode: number;

  @DeletedAt
  @Column
  declare deletedAt: Date;
}
