import { Injectable } from '@nestjs/common';
import * as WebSocket from 'ws';
import * as https from 'node:https';

@Injectable()
export class WSService {
  // wss://echo.websocket.org is a test websocket server
  private ws: WebSocket;

  constructor() {}

  connect(wsUrl) {
    // insecure TLS agent
    const agent = new https.Agent({
      rejectUnauthorized: false,
      checkServerIdentity: () => undefined, // domain adı ve sertifika doğrulamasını devre dışı bırakır
    });
    this.ws = new WebSocket(wsUrl,{
      agent,
    });
    console.log(`Connecting to WSS socket:${wsUrl}`);

    this.ws.on('open', () => {
      console.log('connected');
      // this.ws.send(Math.random())
    });

    // this.ws.on("message", function(message) {
    //   console.log("message");
    //   console.log(message);
    // });
  }

  send(data: any) {
    this.ws.send(data, (err) => {
      console.log('event', err);
    });
  }

  onMessage(handler: Function) {
    this.ws.on('message', handler);
  }
}
