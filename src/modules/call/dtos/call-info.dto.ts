import { ApiProperty } from '@nestjs/swagger';
export class DynamicInfoModel {
  company: string;
  dynamicnumber: string;
  did: string;
  cli: string;
  cld: string;
  uniqueid: string;
  lastapp: string;
  lastaction: string;
  dtmf: string;
  seq: string;
  seqall: string;
  message: string;
  variable: string;
  variable2: string;
  ivrvar: string;
  speechtext: string;

  tx_socket: string;
  rx_socket: string;
}

export class CallInfoDto {
  @ApiProperty()
  dynamic: DynamicInfoModel;
}

