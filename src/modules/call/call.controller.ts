import { Body, Controller, Get, Post } from '@nestjs/common';
import { CallService } from './call.service';
import { ApiTags } from '@nestjs/swagger';
import { CallInfoDto } from './dtos/call-info.dto';

@ApiTags('voice')
@Controller('voice')
export class CallController {
  constructor(private readonly callService: CallService) {}

  @Get('incoming')
  startGet(@Body() createCallDto: CallInfoDto) {
    console.log('get', createCallDto);
    const fs = require('fs');

    const wavData = fs.readFileSync('./uploads/mono.wav');
    console.log('written', wavData);
    return wavData.toString('base64');
  }

  @Post('incoming')
  start(@Body() callInfoDto: CallInfoDto) {
    // console.log('data', callInfoDto);
    console.log('Api call', callInfoDto.dynamic?.lastapp);

    if (callInfoDto.dynamic?.lastapp === 'firststart') {
      // return { application: 'STREAM', volume: 8, timeout: 30, wait: 1 };

      return {
        application: 'STREAM',
        silence_threshold: '500',
        silence_duration: '1000',
        samplerate: '16000',
        volume: 1,
        timeout: 60,
        wait: 1,
      };
    }
    if (callInfoDto.dynamic?.lastapp === 'ENDSTREAM') {
      return { status: 'end' };
    }

    return this.callService.incoming(callInfoDto);
  }
}
