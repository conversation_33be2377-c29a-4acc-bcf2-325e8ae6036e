import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CallController } from './call.controller';
import { CallService } from './call.service';
import { WSService } from './wss.service';
import { SendVoiceService } from './send-voice.service';
import { RealtimeModule } from '../realtime/realtime.module';

@Module({
  controllers: [CallController],
  providers: [CallService, WSService, SendVoiceService],
  imports: [RealtimeModule],
})
export class CallModule {}
