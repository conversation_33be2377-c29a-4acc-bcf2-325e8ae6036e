import { Injectable } from '@nestjs/common';
import { CallInfoDto } from './dtos/call-info.dto';
import { WSService } from './wss.service';
import { SendVoiceService } from './send-voice.service';
import { RealtimeService } from '../realtime/realtime.service';
import { TranscribeService } from '../realtime/transcribe.service';
import * as fs from 'fs';
import { AudioService } from '../realtime/audio.service';

@Injectable()
export class CallService {
  private useTranscribe = true;

  public constructor(
    protected readonly wsService: WSService,
    protected readonly sendVoiceService: SendVoiceService,
    protected readonly realtimeService: RealtimeService,
    protected readonly transcribeService: TranscribeService,
    protected readonly audioService: AudioService
  ) {}

  public async incoming(callInfo: CallInfoDto) {
    try {
      this.wsService.connect(callInfo.dynamic.tx_socket);

      this.sendVoiceService
        .connect(callInfo.dynamic.rx_socket)
        .then(async () => {
          const responseWav = fs.readFileSync('uploads/notification.wav');
          this.sendVoiceService.sendVoice({
            application: '',
            streamfile: responseWav.toString('base64'),
            uniqueid: callInfo.dynamic.uniqueid,
          });
        });

      await this.realtimeService.start();

      this.wsService.onMessage(async (message) => {
        console.log('Message from Santral len:', message.length);
        // if (message.length < 14000) {
        if (message.length < 55000) {
          // ufak tefek takirtilar sorun oluyor
          console.log('Message too short, skipping');
          return;
        }

        if (!this.sendVoiceService.isConnected) {
          await this.sendVoiceService.connect(callInfo.dynamic.rx_socket);
          console.log('Reconnected');
        }

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `./uploads/call-recording-${callInfo.dynamic.uniqueid}-${timestamp}.wav`;

        const parsed = JSON.parse(message);
        // console.log('parsed', parsed);

        const rawWav = Buffer.from(parsed.stream, 'base64');
        fs.writeFileSync(filename, rawWav);

        if (this.useTranscribe) {
          console.log('Transcribing audio file:', filename);
          const transcribedText = await this.transcribeService.transcribeWavFile(filename);
          console.log('Transcribed text:', transcribedText);

          this.realtimeService.sendTextMessage(
            transcribedText,
            (responseWav) => {
              // console.log('responseWav sent', responseWav);
              const success = this.sendVoiceService.sendVoice({
                application: '',
                streamfile: responseWav,
                uniqueid: callInfo.dynamic.uniqueid,
              });
              if (success) {
                console.log(`Voice data sent successfully.`);
              } else {
                console.error(`Failed to send voice data.`);
              }
            }
          );
        } else {
          this.realtimeService.sendWaveAudio(filename, (responseWav) => {
            const success = this.sendVoiceService.sendVoice({
              application: '',
              streamfile: responseWav,
              uniqueid: callInfo.dynamic.uniqueid,
            });
            if (success) {
              console.log(`Voice data sent successfully (fallback).`);
            } else {
              console.error(`Failed to send voice data (fallback).`);
            }
          });
        }
      });

      return { status: 'processing' };
    } catch (error) {
      console.error('Error establishing connections:', error);
      return { status: 'error', message: 'Failed to establish connections' };
    }
  }
}
