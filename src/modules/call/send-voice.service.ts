import { Injectable } from '@nestjs/common';
import * as tls from 'tls';
import { URL } from 'url';

@Injectable()
export class SendVoiceService {
  private tlsSocket: tls.TLSSocket | null = null;
  private messageHandlers: Function[] = [];
  isConnected = false;

  constructor() {}

  connect(tlsUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Parse the TLS URL (e.g., "tls://socket2.ncvav.com:14948")
        const url = new URL(tlsUrl);
        const host = url.hostname;
        const port = parseInt(url.port) || 443;

        console.log(`Connecting to TLS socket: ${host}:${port}`);

        // Create TLS connection
        this.tlsSocket = tls.connect({
          host,
          port,
          rejectUnauthorized: false, // Allow self-signed certificates
        }, () => {
          console.log('TLS send voice service connected');
          this.isConnected = true;
          resolve();
        });

        // Handle connection errors
        this.tlsSocket.on('error', (error) => {
          console.error('TLS send voice service error:', error);
          this.isConnected = false;
          reject(error);
        });

        // Handle connection close
        this.tlsSocket.on('close', () => {
          console.log('TLS send voice service connection closed');
          this.isConnected = false;
        });

        // Handle incoming data
        this.tlsSocket.on('data', (data) => {
          console.log('TLS send voice service received data:', data.toString());
          // Call all registered message handlers
          this.messageHandlers.forEach(handler => {
            try {
              handler(data);
            } catch (error) {
              console.error('Error in message handler:', error);
            }
          });
        });

      } catch (error) {
        console.error('Error parsing TLS URL:', error);
        reject(error);
      }
    });
  }

  sendVoice(data: { streamfile: string; uniqueid: string, application: string }): boolean {
    if (!this.tlsSocket || !this.isConnected) {
      console.error('TLS socket not connected');
      return false;
    }

    try {
      // Convert data to JSON string and send
      const jsonData = JSON.stringify(data);
      this.tlsSocket.write(jsonData); // Add newline as delimiter
      console.log('Voice data sent via TLS');
      this.tlsSocket.write('\n');
      this.tlsSocket.end();

      return true;
    } catch (error) {
      console.error('Error sending voice data:', error);
      return false;
    }
  }

  onMessage(handler: Function): void {
    this.messageHandlers.push(handler);
  }

  disconnect(): void {
    if (this.tlsSocket) {
      this.tlsSocket.end();
      this.tlsSocket = null;
      this.isConnected = false;
      this.messageHandlers = [];
    }
  }

  isSocketConnected(): boolean {
    return this.isConnected;
  }
}
