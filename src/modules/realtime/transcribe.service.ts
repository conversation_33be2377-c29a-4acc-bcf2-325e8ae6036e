import { Injectable } from '@nestjs/common';
import { OpenAI } from 'openai';
import * as fs from 'fs';
import * as process from 'node:process';
import { LocalTranscribeService } from './local-transcribe.service';

@Injectable()
export class TranscribeService {
  private openai: OpenAI;
  private useLocalTranscribe: boolean;

  constructor(private readonly localTranscribeService: LocalTranscribeService) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.useLocalTranscribe = process.env.USE_LOCAL_TRANSCRIBE === 'true';
  }


  async transcribeWavFile(filePath: string): Promise<string> {
    if (this.useLocalTranscribe) {
      console.log('Using local transcription service');
      return this.localTranscribeService.transcribeWavFile(filePath);
    }

    try {
      console.log('Using OpenAI transcription service');
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const audioFile = fs.createReadStream(filePath);

      const transcription = await this.openai.audio.transcriptions.create({
        file: audioFile,
        model: process.env.OPENAI_API_TRANSCRIBE_MODEL || 'whisper-1',
        language: 'tr',
        response_format: 'text',
      });

      // console.log('Transcription result:', transcription);
      return transcription;
    } catch (error) {
      console.error('Error transcribing audio:', error);
      throw new Error(`Transcription failed: ${error.message}`);
    }
  }

}
