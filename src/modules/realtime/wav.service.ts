import { Injectable } from '@nestjs/common';
import * as ffmpeg from 'fluent-ffmpeg';

@Injectable()
export class WavService {

  // WAV'ı PCM'e çevir
  async convertWavToPcm(wavBuffer) {
    try {
      // Header'ı parse et
      const pcmDataOffset = 44;

      // PCM data'yı çıkar
      return wavBuffer.slice(pcmDataOffset);
    } catch (error) {
      console.error('Hata:', error.message);
      return { success: false, error: error.message };
    }
  }


  convertPCM8kTo16k(inputFile, outputFile) {
    return new Promise((resolve, reject) => {
      ffmpeg(inputFile)
        .inputFormat('s16le') // 16-bit signed little endian PCM
        .inputOptions([
          '-ar 8000', // Input sample rate 8kHz
          '-ac 1'     // Mono channel (tek kanal)
        ])
        .audioFrequency(16000) // Output sample rate 16kHz
        .audioChannels(1)      // Output mono
        .audioCodec('pcm_s16le') // PCM 16-bit signed little endian
        .format('wav') // WAV formatında çıktı (isteğe bağlı)
        .on('start', (commandLine) => {
          console.log('FFmpeg komutu başlatıldı:', commandLine);
        })
        .on('progress', (progress) => {
          console.log('İşlem durumu:', progress.percent + '% tamamlandı');
        })
        .on('end', () => {
          console.log('Dönüştürme tamamlandı!');
          resolve(outputFile);
        })
        .on('error', (err) => {
          console.error('Hata:', err.message);
          reject(err);
        })
        .save(outputFile);
    });
  }


}
