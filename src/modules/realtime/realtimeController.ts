import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { RealtimeService } from './realtime.service';
import { TranscribeService } from './transcribe.service';
import * as fs from 'fs';
import { AudioService } from './audio.service';
import { WavService } from './wav.service';

@ApiTags('realtime')
@Controller('realtime')
export class RealtimeController {
  constructor(
    private readonly realtimeService: RealtimeService,
    private readonly transcribeService: TranscribeService,
    private readonly audioService: AudioService,
    private readonly wavService: WavService
  ) {}

  @Get('start')
  async start() {
    // const wavData = fs.readFileSync('./uploads/mono.wav');
    const buffer = fs.readFileSync(
      './uploads/call-recording-151-1750547638.44356-2025-06-21T23-14-38-695Z.wav'
    );
    // const buffer =  wavData.toString('base64');

    await this.realtimeService.start();

    const files = ['./uploads/download.wav'];

    for (const filename of files) {
      const audioFile = fs.readFileSync(filename);
      const audioBuffer = await this.audioService.convertWavToPCM16(audioFile);
      this.realtimeService.sendChunk(audioBuffer);
    }

    // this.realtimeService.sendChunk(buffer);
    this.realtimeService.responseCreate();

    return {
      message: 'Realtime started',
    };
  }

  @Get('convert')
  async convert() {
    const buffer = fs.readFileSync('uploads/model-response.bin');
    const sample = 24000;

    const wavBuffer = this.audioService.createWavFileFromPCM(buffer, sample);
    fs.writeFileSync(
      `uploads/model-response-converted-${sample}.wav`,
      wavBuffer
    );
    console.log('WAV dosyası oluşturuldu: output.wav');
  }

  @Get('ffmpeg')
  async ffmpeg() {
    const inputFile = 'uploads/model-response.bin';
    const outputFile = 'uploads/output.wav';

    const wavBuffer = await this.audioService.convertPcmToWavBinary8Khz(inputFile,outputFile);

    return {}
    // fs.writeFileSync(outputFile, wavBuffer);

    //
    // ffmpeg()
    //   .input(inputFile)
    //   .inputFormat('s16le')  // PCM16LE formatı
    //   .inputOptions([
    //     '-ar 24000',       // Giriş sample rate: 24kHz
    //     '-ac 1'            // Mono kanal
    //   ])
    //   .audioCodec('pcm_s16le')   // WAV için PCM codec
    //   .audioFrequency(8000)      // Çıkış sample rate: 8kHz
    //   .audioChannels(1)          // Mono kanal çıkış
    //   .output(outputFile)
    //   .on('start', (commandLine) => {
    //     console.log('FFmpeg komutu:', commandLine);
    //     console.log('Dönüştürme başladı...');
    //   })
    //   .on('progress', (progress) => {
    //     console.log(`İşlem: ${progress.percent ? progress.percent.toFixed(2) : '?'}%`);
    //   })
    //   .on('end', () => {
    //     console.log('Dönüştürme tamamlandı!');
    //   })
    //   .on('error', (err) => {
    //     console.error('Hata:', err.message);
    //   })
    //   .run();
  }

  @Get('pcm')
  async pcm() {
    const inputBuffer = fs.readFileSync('uploads/record1.wav');

    const pcmBuffer = await this.wavService.convertWavToPcm(inputBuffer);
    fs.writeFileSync('uploads/output2.pcm', pcmBuffer);
  }


  @Get('test')
  async test() {
    await this.realtimeService.start();

    await new Promise(resolve => setTimeout(resolve, 1000));
    const rawWav = fs.readFileSync('uploads/baskentFake16.wav');

    this.realtimeService.sendWaveAudio('uploads/baskent8.wav', (responseWav) => {
      // console.log('responseWav', responseWav);

    });

    return {}
  }

  @Get('test-transcribe')
  async testTranscribe() {
    try {
      // Test transcription with an existing audio file
      const audioFile = 'uploads/call-recording-111-1750613511.15401-2025-06-22T17-32-04-575Z.wav';

      if (!fs.existsSync(audioFile)) {
        return { error: 'Test audio file not found', file: audioFile };
      }

      console.log('Testing transcription with file:', audioFile);
      const transcribedText = await this.transcribeService.transcribeWavFile(audioFile);

      return {
        success: true,
        file: audioFile,
        transcribedText: transcribedText
      };
    } catch (error) {
      console.error('Transcription test error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  @Get('test-text-message')
  async testTextMessage() {
    try {
      await this.realtimeService.start();
      await new Promise(resolve => setTimeout(resolve, 1000));

      const testText = 'Merhaba, nasılsınız?';
      console.log('Testing text message:', testText);

      return new Promise((resolve) => {
        this.realtimeService.sendTextMessage(testText, (responseWav) => {
          console.log('Response received from text message');
          resolve({
            success: true,
            inputText: testText,
            responseReceived: true,
            responseLength: responseWav ? responseWav.length : 0
          });
        });
      });
    } catch (error) {
      console.error('Text message test error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  @Get('test-full-flow')
  async testFullFlow() {
    try {
      // Test the complete transcription + text message flow
      const audioFile = 'uploads/baskent8.wav';

      if (!fs.existsSync(audioFile)) {
        return { error: 'Test audio file not found', file: audioFile };
      }

      console.log('Testing full transcription + text message flow');

      // Step 1: Transcribe audio
      const transcribedText = await this.transcribeService.transcribeWavFile(audioFile);
      console.log('Transcribed text:', transcribedText);

      // Step 2: Start realtime service
      await this.realtimeService.start();
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 3: Send transcribed text as message
      return new Promise((resolve) => {
        this.realtimeService.sendTextMessage(transcribedText, (responseWav) => {
          console.log('Full flow completed successfully');
          resolve({
            success: true,
            audioFile: audioFile,
            transcribedText: transcribedText,
            responseReceived: true,
            responseLength: responseWav ? responseWav.length : 0
          });
        });
      });
    } catch (error) {
      console.error('Full flow test error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }


}
