import { Injectable } from '@nestjs/common';
import { RawData, WebSocket } from 'ws';
import functions from './functionHandlers';
import { ResponseData } from './types';
import * as process from 'node:process';

@Injectable()
export class OpenAiRealtimeService {
  private modelConn: any;
  private responseStartTimestamp?: number;
  private latestMediaTimestamp: number;
  private lastAssistantItem: any;
  private openAIApiKey: any = process.env.OPENAI_API_KEY;
  private saved_config = {
    // instructions: 'You are a voice-enabled AI customer service assistant working on behalf of Horoz Logistics. Your role is strictly limited to handling inbound calls related to cargo return (iade) inquiries. You speak Turkish only, communicate verbally, and follow a polite, clear, and structured tone. Keep it simple and short.\n\nFollow the rules and constraints below exactly:\n\n🟢 YOUR RESPONSIBILITIES:\n\nConfirm whether the customer is calling about a cargo return.\n\nPolitely guide the conversation with step-by-step instructions.\n\nAsk for the following information:\n\nTracking number or sender\'s phone number\n\nReturn reason (e.g., damaged item, incorrect product, no recipient available, customer didn’t receive, wrong address)\n\nIf the return request is valid, acknowledge it and inform the customer that the process has started.\n\nIf the package must be returned via a physical branch, offer to give directions to the nearest Horoz Logistics branch.\n\nIf the assistant cannot resolve the issue, offer to transfer the call to a human customer service agent.\n\n🔴 RESTRICTIONS AND BOUNDARIES:\n\nDo not provide information about topics outside of Horoz Logistics or beyond the return process.\n\nDo not answer questions about pricing, delivery times, order cancellations, payments, or product details.\n\nDo not request sensitive personal information such as ID numbers, email addresses, or payment data.\n\nDo not make emotional judgments, personal suggestions, or handle complaints (only forward or redirect).\n\n🗣️ COMMUNICATION STYLE:\n\nSpeak in formal, courteous, and concise Turkish.\n\nUse simple language that even non-technical users or elderly callers can understand.\n\nIf you don’t understand something, politely ask:\n“Sizi tam anlayamadım, lütfen tekrar eder misiniz?” (“I didn’t quite catch that, could you please repeat?”)\n\n🧠 ESCALATION CRITERIA:\n\nIf the customer’s request is outside your scope:\n👉 Say: “Bu konuda bir müşteri temsilcimize yönlendirme yapmam gerekiyor.” (“I need to transfer you to a customer representative for this matter.”)\n\nIf the customer uses offensive or abusive language:\n👉 Say: “Size yardımcı olamayacağım durumda görüşmeyi sonlandırıyorum. İyi günler.” (“I’m ending the call as I cannot assist in this situation. Have a nice day.”)\n\nAt the end of every call, always ask:\n“Başka bir konuda yardımcı olabilir miyim?”\nIf all required information has been collected, thank the customer and close the call. Cumlelerin 15 kelimeden fazla olmasin',
    instructions:
      'Müşterinin sorguğu sorulara yanıt ver, Türkçe konuş. Konuşmaların çok uzun olmasın en fazla 10 kelime olsun.',
  };
  private streamSid: any;
  private onResponse: ((data: ResponseData) => void) | null = null;

  constructor() {}

  connect() {
    return new Promise((resolve, reject) => {
      try {
        if (this.isOpen(this.modelConn)) {
          return;
        }

        this.modelConn = new WebSocket(
          'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2025-06-03',
          {
            headers: {
              Authorization: `Bearer ${this.openAIApiKey}`,
              'OpenAI-Beta': 'realtime=v1',
            },
          }
        );

        this.modelConn.on('open', () => {
          resolve(this.modelConn);
          const config = this.saved_config || {};
          this.jsonSend(this.modelConn, {
            type: 'session.update',
            session: {
              modalities: ['text', 'audio'],
              turn_detection: { type: 'server_vad' },
              voice: 'sage',
              input_audio_transcription: {
                model: process.env.OPENAI_API_TRANSCRIBE_MODEL,
                language: 'tr',
              },
              input_audio_noise_reduction: { type: 'near_field' },

              // input_audio_format: 'g711_ulaw',
              // output_audio_format: 'g711_ulaw',
              input_audio_format: 'pcm16',
              output_audio_format: 'pcm16',
              ...config,
            },
          });
        });

        this.modelConn.on('message', (data: RawData) =>
          this.handleModelMessage(data)
        );
        this.modelConn.on('error', (err: any) => {
          console.error('Model connection error:', err);
          return this.closeModel;
        });
        this.modelConn.on('close', () => this.closeModel);
      } catch (error) {
        console.error('Error parsing TLS URL:', error);
        reject(error);
      }
    });
  }

  isOpen(ws?: WebSocket): ws is WebSocket {
    return !!ws && ws.readyState === WebSocket.OPEN;
  }

  jsonSend(ws: WebSocket | undefined, obj: unknown, cb?: Function) {
    if (!this.isOpen(ws)) {
      return;
    }
    ws.send(JSON.stringify(obj), cb);
  }

  parseMessage(data: RawData): any {
    try {
      return JSON.parse(data.toString());
    } catch {
      return null;
    }
  }

  handleModelMessage(data: RawData) {
    const event = this.parseMessage(data);
    if (!event) {
      return;
    }
    // console.log('event', event.type);

    if (
      event.type.includes('response.audio_transcript.done') ||
      event.type.includes('transcription.completed') ||
      event.type.includes('error')
    ) {
      if (event.transcript) {
        console.log('event transcript', event.transcript);
        return;
      }
      console.log('event detail', {
        ...event,
        delta:
          event?.delta?.length > 200
            ? `${event?.delta.length} bytes`
            : event?.delta,
      });
    }

    switch (event.type) {
      case 'input_audio_buffer.speech_started':
        this.handleTruncation();
        break;
      case 'response.created':
        this.onResponseCreated(event);
        break;

      case 'response.audio.delta':
        if (typeof this.onResponse === 'function' && this.streamSid) {
          if (this.responseStartTimestamp === undefined) {
            this.responseStartTimestamp = this.latestMediaTimestamp || 0;
          }
          if (event.item_id) {
            this.lastAssistantItem = event.item_id;
          }

          this.onResponse({
            event: 'delta',
            streamSid: this.streamSid,
            media: { payload: event.delta },
          });

          // this.jsonSend(this.twilioConn, {
          //   event: 'mark',
          //   streamSid: this.streamSid,
          // });
        }
        break;

      case 'response.output_item.done': {
        const { item } = event;
        // console.log('Done', item);
        if (typeof this.onResponse === 'function' && this.streamSid) {
          this.onResponse({
            event: 'done',
            streamSid: this.streamSid,
          });
        }

        /*        if (item.type === 'function_call') {
                  this.handleFunctionCall(item)
                    .then((output) => {
                      if (this.modelConn) {
                        this.jsonSend(this.modelConn, {
                          type: 'conversation.item.create',
                          item: {
                            type: 'function_call_output',
                            call_id: item.call_id,
                            output: JSON.stringify(output),
                          },
                        });
                        this.jsonSend(this.modelConn, { type: 'response.create' });
                      }
                    })
                    .catch((err) => {
                      console.error('Error handling function call:', err);
                    });
                }*/
        break;
      }
    }
  }

  closeModel() {
    this.cleanupConnection(this.modelConn);
    this.modelConn = undefined;
  }

  cleanupConnection(ws?: WebSocket) {
    if (this.isOpen(ws)) {
      ws.close();
    }
  }

  async handleFunctionCall(item: { name: string; arguments: string }) {
    console.log('Handling function call:', item);
    const fnDef = functions.find((f) => f.schema.name === item.name);
    if (!fnDef) {
      throw new Error(`No handler found for function: ${item.name}`);
    }

    let args: unknown;
    try {
      args = JSON.parse(item.arguments);
    } catch {
      return JSON.stringify({
        error: 'Invalid JSON arguments for function call.',
      });
    }

    try {
      console.log('Calling function:', fnDef.schema.name, args);
      const result = await fnDef.handler(args as any);
      return result;
    } catch (err: any) {
      console.error('Error running function:', err);
      return JSON.stringify({
        error: `Error running function ${item.name}: ${err.message}`,
      });
    }
  }

  handleTruncation() {
    if (!this.lastAssistantItem || this.responseStartTimestamp === undefined) {
      return;
    }

    const elapsedMs =
      (this.latestMediaTimestamp || 0) - (this.responseStartTimestamp || 0);
    const audio_end_ms = elapsedMs > 0 ? elapsedMs : 0;

    if (this.isOpen(this.modelConn)) {
      this.jsonSend(this.modelConn, {
        type: 'conversation.item.truncate',
        item_id: this.lastAssistantItem,
        content_index: 0,
        audio_end_ms,
      });
    }

    // if (this.twilioConn && this.streamSid) {
    //   this.jsonSend(this.twilioConn, {
    //     event: 'clear',
    //     streamSid: this.streamSid,
    //   });
    // }

    this.lastAssistantItem = undefined;
    this.responseStartTimestamp = undefined;
  }

  responseCallback(callback: (data: any) => void) {
    this.onResponse = callback;
  }

  sendToModel(data: any) {
    if (this.modelConn) {
      console.log('Sending to model', data.type);
      this.jsonSend(this.modelConn, data);
    }
  }

  private onResponseCreated(event) {
    this.streamSid = event.response.id;
  }
}
