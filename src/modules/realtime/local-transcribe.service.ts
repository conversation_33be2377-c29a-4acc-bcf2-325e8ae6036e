import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import * as fs from 'fs';
import * as FormData from 'form-data';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class LocalTranscribeService {
  private localTranscribeUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.localTranscribeUrl = process.env.LOCAL_TRANSCRIBE_URL || 'http://localhost:8080';
  }

  async transcribeWavFile(filePath: string): Promise<string> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const formData = new FormData();
      formData.append('file', fs.createReadStream(filePath));
      formData.append('language', 'tr');

      const response:any = await firstValueFrom(
        this.httpService.post(`${this.localTranscribeUrl}/inference`, formData, {
          headers: {
            ...formData.getHeaders(),
          },
          timeout: 30000, // 30 second timeout
        })
      );

      if (response.data && response.data.text) {
        console.log('Local transcription result:', response.data.text);
        return response.data.text;
      } else {
        throw new Error('Invalid response format from local transcription service');
      }
    } catch (error) {
      console.error('Error transcribing audio with local service:', error);
      if (error.response) {
        throw new HttpException(
          `Local transcription failed: ${error.response.status} - ${error.response.statusText}`,
          error.response.status
        );
      }
      throw new HttpException(
        `Local transcription failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

}
