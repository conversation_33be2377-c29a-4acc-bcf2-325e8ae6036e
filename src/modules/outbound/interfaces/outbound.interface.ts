import { Outbound } from '../entities/outbound.entity';

export interface OutboundRecord {
  number: string;
  name: string;
  surname: string;
  status: 'new' | 'calling' | 'completed' | 'failed';
  destination: string;
  rowIndex: number; // Google Sheets row index for updates
  other: any;
}

export interface CallApiRequest {
  application: string;
  destination: string;
  callerid: string;
  responseurl: string;
  variable: string;
  vmdetect: string;
  caller: {
    [key: string]: string;
  };
}

export interface CallApiResponse {
  success: boolean;
  message?: string;
  callId: string;
  requestData: object;
  responseData: object;
  error?: string;
}

export interface OutboundCallResult {
  record: OutboundRecord | Outbound;
  success: boolean;
  callId?: string;
  error?: string;
}

export interface CallVapiApiRequest {
  assistantId: string;
  assistantOverrides: {
    variableValues: Record<string, string>;
  };
  customer: {
    number: string;
  };
  phoneNumberId: string;
}
