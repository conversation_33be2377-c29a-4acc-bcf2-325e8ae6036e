import { Column, DataType, Table, <PERSON><PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { ResourceEntity } from '../../../core/base/resource.entity';
import { Outbound } from './outbound.entity';

@Table({
  tableName: 'outbound_logs',
  indexes: [
    {
      fields: ['outbound_id'],
      unique: false,
    },
    {
      fields: ['call_id'],
      unique: false,
    },
    {
      fields: ['call_status'],
      unique: false,
    },
    {
      fields: ['created_at'],
      unique: false,
    },
  ],
})
export class OutboundLogs extends ResourceEntity<OutboundLogs> {
  @ForeignKey(() => Outbound)
  @Column({
    type: DataType.UUID,
    allowNull: false,
    field: 'outbound_id',
    comment: 'Reference to outbound record',
  })
  declare outboundId: string;

  @BelongsTo(() => Outbound)
  outbound: Outbound;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    field: 'call_id',
    comment: 'Call ID from NCVAV API',
  })
  declare callId: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    field: 'call_status',
    comment: 'Status of the call (initiated, answered, completed, failed, etc.)',
  })
  declare callStatus: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    field: 'caller_id',
    comment: 'Caller ID used for the call',
  })
  declare callerId: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    field: 'response_url',
    comment: 'Response URL used for the call',
  })
  declare responseUrl: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    field: 'api_request',
    comment: 'Full API request sent to NCVAV',
  })
  declare apiRequest: object;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    field: 'api_response',
    comment: 'Full API response from NCVAV',
  })
  declare apiResponse: object;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    field: 'error_message',
    comment: 'Error message if call failed',
  })
  declare errorMessage: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    field: 'call_duration',
    comment: 'Call duration in seconds',
  })
  declare callDuration: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    field: 'call_started_at',
    comment: 'When the call was initiated',
  })
  declare callStartedAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    field: 'call_ended_at',
    comment: 'When the call ended',
  })
  declare callEndedAt: Date;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    field: 'twilio_data',
    comment: 'Twilio webhook data if call was redirected to Bland',
  })
  declare twilioData: object;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    field: 'bland_url',
    comment: 'Bland URL used for redirection',
  })
  declare blandUrl: string;
}
