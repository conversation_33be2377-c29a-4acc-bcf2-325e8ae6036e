import { Column, DataType, Table, Index } from 'sequelize-typescript';
import { ResourceEntity } from '../../../core/base/resource.entity';

@Table({
  tableName: 'outbound',
  indexes: [
    {
      fields: ['number'],
      unique: false,
    },
    {
      fields: ['status'],
      unique: false,
    },
  ],
})
export class Outbound extends ResourceEntity<Outbound> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'First name of the contact',
  })
  declare name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'Last name of the contact',
  })
  declare surname: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Debt amount',
  })
  declare debt: number;

  @Column({
    type: DataType.ENUM('new', 'calling', 'completed', 'failed'),
    allowNull: true,
    comment: 'Status of the outbound call',
  })
  declare status: null | 'new' | 'calling' | 'completed' | 'failed';

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'Destination model',
  })
  declare destination: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Google Sheets row index for updates',
  })
  declare rowIndex: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Last call ID from NCVAV API',
  })
  declare lastCallId: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: 'Last call attempt timestamp',
  })
  declare lastCallAt: Date;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'Phone number of the contact',
  })
  declare number: string;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
    comment: 'Custom data',
  })
  declare other: object;
}
