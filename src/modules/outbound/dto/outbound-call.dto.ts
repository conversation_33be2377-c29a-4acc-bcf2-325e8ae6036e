import { IsNumber, IsOptional, IsString } from 'class-validator';

export class StartOutboundCallsDto {

  @IsOptional()
  @IsString()
  responseurl?: string;

  @IsOptional()
  @IsNumber()
  maxCalls?: number;
}

export class OutboundCallStatusDto {
  @IsString()
  number: string;

  @IsString()
  name: string;

  @IsString()
  surname: string;

  @IsNumber()
  debt: number;

  @IsString()
  status: string;

  @IsOptional()
  @IsString()
  callId?: string;

  @IsOptional()
  @IsString()
  error?: string;
}
