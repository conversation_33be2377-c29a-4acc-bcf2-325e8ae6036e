import { Body, Controller, Get, Post, Param, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { OutboundService } from './outbound.service';
import { StartOutboundCallsDto } from './dto/outbound-call.dto';
import { OutboundEntityService } from './services/outbound-entity.service';
import { OutboundLogsService } from './services/outbound-logs.service';

@ApiTags('outbound')
@Controller('outbound')
export class OutboundController {
  constructor(
    private readonly outboundService: OutboundService,
    private readonly outboundEntityService: OutboundEntityService,
    private readonly outboundLogsService: OutboundLogsService,
  ) {}

  @Post('start-calls')
  async startOutboundCalls(@Body() dto: StartOutboundCallsDto) {
    try {
      // First sync Excel data with database
      await this.outboundService.syncExcelWithDatabase();

      // start calls from DB
      const results = await this.outboundService.startOutboundCalls(dto);

      return {
        success: true,
        message: `Processed ${results.length} calls`,
        data: results,
        summary: {
          total: results.length,
          successful: results.filter((r) => r.success).length,
          failed: results.filter((r) => !r.success).length,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to start outbound calls',
        error: error.message,
      };
    }
  }

  @Get('records')
  async getOutboundRecords() {
    try {
      const records = await this.outboundService.getOutboundRecords();

      return {
        success: true,
        message: `Found ${records.length} records`,
        data: records,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to get outbound records',
        error: error.message,
      };
    }
  }

  @Get('new-records')
  async getNewRecords() {
    try {
      const records = await this.outboundService.getNewRecords();

      return {
        success: true,
        message: `Found ${records.length} new records`,
        data: records,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to get new records',
        error: error.message,
      };
    }
  }

  @Get('test-api')
  async testApiConnection() {
    try {
      const result = await this.outboundService.testApiConnection();

      return {
        success: result.success,
        message: result.message,
      };
    } catch (error) {
      return {
        success: false,
        message: 'API connection test failed',
        error: error.message,
      };
    }
  }

  @Post('sync-excel')
  async syncExcelWithDatabase() {
    try {
      await this.outboundService.syncExcelWithDatabase();
      return {
        success: true,
        message: 'Excel data synced with database successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to sync Excel data with database',
        error: error.message,
      };
    }
  }

  @Get('database-records')
  async getDatabaseRecords() {
    try {
      const records = await this.outboundEntityService.findAll();
      return {
        success: true,
        message: `Found ${records.length} records in database`,
        data: records,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to get database records',
        error: error.message,
      };
    }
  }

  @Get('database-records/status/:status')
  async getDatabaseRecordsByStatus(@Param('status') status: 'new' | 'calling' | 'completed' | 'failed') {
    try {
      const records = await this.outboundEntityService.findByStatus(status);
      return {
        success: true,
        message: `Found ${records.length} records with status '${status}'`,
        data: records,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get records with status '${status}'`,
        error: error.message,
      };
    }
  }

  @Get('logs')
  async getOutboundLogs(@Query('limit') limit?: number, @Query('offset') offset?: number) {
    try {
      const logs = await this.outboundLogsService.getAllLogs(limit || 100, offset || 0);
      return {
        success: true,
        message: `Found ${logs.length} log entries`,
        data: logs
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to get outbound logs',
        error: error.message,
      };
    }
  }

  @Get('logs/outbound/:outboundId')
  async getLogsByOutboundId(@Param('outboundId') outboundId: string) {
    try {
      const logs = await this.outboundLogsService.getLogsByOutboundId(outboundId);
      return {
        success: true,
        message: `Found ${logs.length} log entries for outbound record`,
        data: logs,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to get logs for outbound record',
        error: error.message,
      };
    }
  }
}
