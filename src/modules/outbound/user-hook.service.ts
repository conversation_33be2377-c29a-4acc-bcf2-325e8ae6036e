import { Injectable } from '@nestjs/common';
import { OutboundEntityService } from '../outbound/services/outbound-entity.service';
import { CustomLoggerService } from '../../shared/custom-logger.service';
import { ElevenHookDto } from '../twilio/dtos/eleven-hook.dto';

@Injectable()
export class UserHookService {
  constructor(
    private readonly logger: CustomLoggerService,
    private readonly outboundEntityService: OutboundEntityService
  ) {}

  async handleTwilioIncoming(twilioPayload: ElevenHookDto) {
    this.logger.log(`User hook from ${twilioPayload.caller_id} to Eleven`);

    const number = twilioPayload.caller_id.startsWith('0')
      ? `9${twilioPayload.caller_id}`
      : twilioPayload.caller_id;
    const outbound = await this.outboundEntityService.findByNumber(number);
    if (!outbound) {
      return null;
    }

    return {
      name: outbound.name,
      surname: outbound.surname,
      ...outbound.other,
    };
  }
}
