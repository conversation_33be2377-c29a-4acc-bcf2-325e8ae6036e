import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { OutboundController } from './outbound.controller';
import { OutboundService } from './outbound.service';
import { GoogleSheetsService } from './google-sheets.service';
import { CallApiService } from './call-api.service';
import { OutboundEntityService } from './services/outbound-entity.service';
import { OutboundLogsService } from './services/outbound-logs.service';
import { outboundProviders } from './outbound.providers';
import { DatabaseModule } from '../../core/database/database.module';
import { TwilioModule } from '../twilio/twilio.module';
import { UserHookService } from './user-hook.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    DatabaseModule,
  ],
  controllers: [OutboundController],
  providers: [
    OutboundService,
    GoogleSheetsService,
    CallApiService,
    OutboundEntityService,
    OutboundLogsService,
    ...outboundProviders,
    UserHookService
  ],
  exports: [OutboundService, OutboundEntityService, OutboundLogsService,UserHookService],
})
export class OutboundModule {}
