import { Injectable, Inject } from '@nestjs/common';
import { OutboundLogs } from '../entities/outbound-logs.entity';
import { Outbound } from '../entities/outbound.entity';
import { TwilioIncomingDto } from '../../twilio/dtos/twilio-incoming.dto';

export interface CreateLogData {
  outboundId: string;
  callId?: string;
  callStatus: string;
  callerId?: string;
  responseUrl?: string;
  apiRequest?: object;
  apiResponse?: object;
  errorMessage?: string;
  callDuration?: number;
  callStartedAt?: Date;
  callEndedAt?: Date;
  twilioData?: object;
  blandUrl?: string;
}

@Injectable()
export class OutboundLogsService {
  constructor(
    @Inject('OutboundLogsRepository')
    private readonly outboundLogsRepository: typeof OutboundLogs,
  ) {}

  /**
   * Create a new outbound call log
   */
  async createLog(data: CreateLogData): Promise<OutboundLogs> {
    return this.outboundLogsRepository.create(data as any);
  }

  /**
   * Log call initiation
   */
  async logCallInitiated(
    outboundId: string,
    callId: string,
    responseUrl: string,
    apiRequest: object,
    apiResponse: object,
  ): Promise<OutboundLogs> {
    return this.createLog({
      outboundId,
      callId,
      callStatus: 'initiated',
      responseUrl,
      apiRequest,
      apiResponse,
      callStartedAt: new Date(),
    });
  }

  /**
   * Log successful API response
   */
  async logApiResponse(
    logId: string,
    apiResponse: object,
    callStatus: string = 'api_success',
  ): Promise<void> {
    await this.outboundLogsRepository.update(
      {
        apiResponse,
        callStatus,
      },
      {
        where: { id: logId },
      },
    );
  }

  /**
   * Log call failure
   */
  async logCallFailed(
    outboundId: string,
    errorMessage: string,
    apiRequest?: object,
    apiResponse?: object,
  ): Promise<OutboundLogs> {
    return this.createLog({
      outboundId,
      callStatus: 'failed',
      errorMessage,
      apiRequest,
      apiResponse,
      callStartedAt: new Date(),
      callEndedAt: new Date(),
    });
  }

  /**
   * Log Twilio redirection to Bland
   */
  async logTwilioRedirection(
    outboundId: string,
    twilioData: TwilioIncomingDto,
  ): Promise<OutboundLogs> {
    return this.createLog({
      outboundId,
      callStatus: 'redirected_to_bland',
      twilioData,
      callStartedAt: new Date(),
    });
  }

  /**
   * Update call completion
   */
  async logCallCompleted(
    logId: string,
    callDuration?: number,
    callStatus: string = 'completed',
  ): Promise<void> {
    await this.outboundLogsRepository.update(
      {
        callStatus,
        callDuration,
        callEndedAt: new Date(),
      },
      {
        where: { id: logId },
      },
    );
  }

  /**
   * Get logs for a specific outbound record
   */
  async getLogsByOutboundId(outboundId: string): Promise<OutboundLogs[]> {
    return this.outboundLogsRepository.findAll({
      where: { outboundId },
      include: [Outbound],
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * Get logs by call ID
   */
  async getLogsByCallId(callId: string): Promise<OutboundLogs[]> {
    return this.outboundLogsRepository.findAll({
      where: { callId },
      include: [Outbound],
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * Get all logs with pagination
   */
  async getAllLogs(limit: number = 100, offset: number = 0): Promise<OutboundLogs[]> {
    return this.outboundLogsRepository.findAll({
      include: [Outbound],
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });
  }

  /**
   * Get logs by status
   */
  async getLogsByStatus(callStatus: string): Promise<OutboundLogs[]> {
    return this.outboundLogsRepository.findAll({
      where: { callStatus },
      include: [Outbound],
      order: [['createdAt', 'DESC']],
    });
  }
}
