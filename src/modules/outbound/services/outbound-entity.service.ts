import { Injectable, Inject } from '@nestjs/common';
import { Outbound } from '../entities/outbound.entity';
import { OutboundRecord } from '../interfaces/outbound.interface';

@Injectable()
export class OutboundEntityService {
  constructor(
    @Inject('OutboundRepository')
    private readonly outboundRepository: typeof Outbound,
  ) {}

  /**
   * Find outbound record by phone number
   */
  async findByNumber(number: string): Promise<Outbound | null> {
    number = number.replace('+', '');
    return this.outboundRepository.findOne({
      where: { number },
      order: [['lastCallAt', 'DESC']],
    });
  }

  /**
   * Create or update outbound record from Excel data
   */
  async createOrUpdate(record: OutboundRecord): Promise<Outbound> {
    const existingRecord = await this.findByNumber(record.number);

    if (existingRecord) {
      // Update existing record
      await existingRecord.update(record);
      return existingRecord;
    } else {
      // Create new record
      return this.outboundRepository.create(record as any);
    }
  }

  /**
   * Sync multiple records from Excel data
   */
  async syncFromExcel(records: OutboundRecord[]): Promise<void> {
    for (const record of records) {
      await this.createOrUpdate(record);
    }
  }

  /**
   * Update call information after making a call
   */
  async updateCallInfo(
    id: string,
    callId: string,
    status: 'calling' | 'completed' | 'failed',
  ): Promise<void> {
    await this.outboundRepository.update(
      {
        lastCallId: callId,
        lastCallAt: new Date(),
        status,
      },
      {
        where: { id },
      },
    );
  }

  /**
   * Get all records with specific status
   */
  async findByStatus(status: 'new' | 'calling' | 'completed' | 'failed'): Promise<Outbound[]> {
    return this.outboundRepository.findAll({
      where: { status },
      order: [['createdAt', 'ASC']],
    });
  }

  /**
   * Get records that need to be called (new status)
   */
  async getNewRecords(): Promise<Outbound[]> {
    return this.findByStatus('new');
  }

  /**
   * Get all outbound records
   */
  async findAll(): Promise<Outbound[]> {
    return this.outboundRepository.findAll({
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * Delete outbound record
   */
  async delete(id: string): Promise<void> {
    await this.outboundRepository.destroy({
      where: { id },
    });
  }
}
