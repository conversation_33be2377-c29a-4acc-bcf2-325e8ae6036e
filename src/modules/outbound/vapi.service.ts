import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import {
  CallApiResponse,
  CallVapiApiRequest,
  OutboundRecord,
} from './interfaces/outbound.interface';
import * as process from 'node:process';
import { CustomLoggerService } from '../../shared/custom-logger.service';
import { UserHookService } from './user-hook.service';
import { Outbound } from './entities/outbound.entity';

export interface VapiAssistant {
  id: string;
  name: string;
  voice?: any;
  model?: any;
  firstMessage?: string;
  // Add other fields as needed
}

export interface VapiAssistantListResponse {
  data?: VapiAssistant[];
  // Add other response fields as needed
}

@Injectable()
export class VapiService {
  private readonly vapiApiUrl = process.env.VAPI_URL || 'https://api.vapi.ai/call/phone';
  private readonly vapiAssistantUrl = process.env.VAPI_ASSISTANT_URL || 'https://api.vapi.ai/assistant';
  private readonly vapiToken = process.env.VAPI_API_KEY;

  constructor(
    private readonly httpService: HttpService,
    private readonly logger: CustomLoggerService,
    private readonly userHookService: UserHookService
  ) {}

  /**
   * Fetch all assistants from VAPI API
   */
  async getAssistants(): Promise<VapiAssistant[]> {
    try {
      this.logger.log('Fetching VAPI assistants...');

      const response = await firstValueFrom(
        this.httpService.get(this.vapiAssistantUrl, {
          headers: {
            Authorization: `Bearer ${this.vapiToken}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        })
      );

      this.logger.log(`VAPI assistants response: ${JSON.stringify(response.data)}`);

      // Handle different response formats
      if (Array.isArray(response.data)) {
        return response.data;
      } else if (response.data?.data && Array.isArray(response.data.data)) {
        return response.data.data;
      } else {
        this.logger.warn('Unexpected VAPI assistants response format');
        return [];
      }
    } catch (error) {
      this.logger.error('Error fetching VAPI assistants:', error);
      throw new Error(`Failed to fetch VAPI assistants: ${error.message}`);
    }
  }

  /**
   * Find assistant by name
   */
  async findAssistantByName(name: string): Promise<VapiAssistant | null> {
    try {
      const assistants = await this.getAssistants();
      const assistant = assistants.find(a => a.name === name);
      
      if (assistant) {
        this.logger.log(`Found VAPI assistant: ${assistant.name} (ID: ${assistant.id})`);
        return assistant;
      } else {
        this.logger.warn(`No VAPI assistant found with name: ${name}`);
        return null;
      }
    } catch (error) {
      this.logger.error(`Error finding assistant by name ${name}:`, error);
      throw error;
    }
  }

  /**
   * Make a VAPI call using dynamic assistant lookup
   */
  async makeCall(
    record: OutboundRecord | Outbound,
    responseurl?: string,
    destinationNumber?: string
  ): Promise<CallApiResponse> {
    try {
      // Find the assistant by destination name
      const assistant = await this.findAssistantByName(record.destination);
      
      if (!assistant) {
        return {
          success: false,
          error: `No VAPI assistant found with name: ${record.destination}`,
          callId: '',
          requestData: {},
          responseData: {},
        };
      }

      const data = await this.userHookService.handleTwilioIncoming({
        caller_id: record.number,
      });

      const requestData: CallVapiApiRequest = {
        assistantId: assistant.id,
        assistantOverrides: {
          variableValues: data || {},
        },
        customer: {
          number: '+' + record.number, // add +
        },
        phoneNumberId: '1ad65894-5042-42a0-b444-28147ba6bb7c',
      };

      this.logger.log(`VAPI Making call to ${record.number} (${record.name} ${record.surname}) using assistant ${assistant.name} (${assistant.id})`);
      this.logger.log('VAPI Request data:' + JSON.stringify(requestData));

      const response = await firstValueFrom(
        this.httpService.post(this.vapiApiUrl, requestData, {
          headers: {
            Authorization: `Bearer ${this.vapiToken}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        })
      );

      this.logger.log('VAPI API Response:' + JSON.stringify(response.data));

      if (response.status === 200 || response.status === 201) {
        return {
          success: true,
          message: 'VAPI call initiated successfully',
          requestData: requestData,
          responseData: response.data,
          callId: response.data?.callinfo?.[0]?.uniqueid || `vapi_call_${Date.now()}`,
        };
      } else {
        return {
          success: false,
          error: `VAPI API returned status ${response.status}`,
          callId: '',
          requestData: requestData,
          responseData: response.data,
        };
      }
    } catch (error) {
      this.logger.error(`Error making VAPI call to ${record.number}:`, error);

      let errorMessage = 'Unknown error occurred';
      if (error.response) {
        errorMessage = `VAPI API Error: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        errorMessage = 'Network error: No response received from VAPI API';
      } else {
        errorMessage = error.message || 'VAPI request setup error';
      }

      return {
        success: false,
        error: errorMessage,
        callId: '',
        requestData: {},
        responseData: {},
      };
    }
  }
}
