import { Injectable } from '@nestjs/common';
import { google } from 'googleapis';
import { OutboundRecord } from './interfaces/outbound.interface';
import { Outbound } from './entities/outbound.entity';

@Injectable()
export class GoogleSheetsService {
  private sheets;
  private readonly spreadsheetId =
    '1ZsX6tOfNW5mWkeHeap5GUXFDMti3pc8zjItuV-Dqs68';
  private readonly range = 'Sheet1!A:L'; // columns A-L contains data

  constructor() {
    let auth;

    if (
      process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL &&
      process.env.GOOGLE_PRIVATE_KEY
    ) {
      auth = new google.auth.GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/spreadsheets'],
        credentials: {
          client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
          private_key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        },
      });
    } else if (process.env.GOOGLE_SHEETS_API_KEY) {
      auth = process.env.GOOGLE_SHEETS_API_KEY;
    } else {
      console.warn(
        'No Google Sheets credentials found. Please set GOOGLE_SERVICE_ACCOUNT_EMAIL and GOOGLE_PRIVATE_KEY or GOOGLE_SHEETS_API_KEY in environment variables.'
      );
      auth = new google.auth.GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      });
    }

    this.sheets = google.sheets({ version: 'v4', auth });
  }

  async getRecordsWithNewStatus(): Promise<OutboundRecord[]> {
    const rows = await this.getAllRecords();
    return rows.filter((record) => record.status === 'new');
  }

  async updateRecordStatus(
    record: OutboundRecord | Outbound,
    newStatus: string
  ): Promise<void> {
    try {
      if (!record.rowIndex) {
        throw new Error('Record row index is required for update');
      }

      const range = `Sheet1!D${record.rowIndex}`; // Column D is status column

      await this.sheets.spreadsheets.values.update({
        spreadsheetId: this.spreadsheetId,
        range: range,
        valueInputOption: 'RAW',
        requestBody: {
          values: [[newStatus]],
        },
      });

      console.log(`Updated status for ${record.number} to ${newStatus}`);
    } catch (error) {
      console.error('Error updating Google Sheets:', error);
      throw new Error(`Failed to update Google Sheets: ${error.message}`);
    }
  }

  async getAllRecords(): Promise<OutboundRecord[]> {
    try {
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: this.spreadsheetId,
        range: this.range,
      });

      const rows = response.data.values;
      if (!rows || rows.length <= 1) {
        return []; // No data or only header row
      }

      const records: OutboundRecord[] = [];

      const headerRow = rows[0]?.slice(5); // skip default fields

      for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        if (row.length >= 4) {
          const [number, name, surname, status, destination,...rest] = row;
          const other = Object.fromEntries(headerRow.map((key, i) => [key, rest[i] || '']))

          records.push({
            number: number?.toString() || '',
            name: name?.toString() || '',
            surname: surname?.toString() || '',
            destination: destination?.toString() || '',
            status: (status?.toString() || 'new') as any,
            other: other,
            rowIndex: i + 1, // Google Sheets is 1-indexed
          });
        }
      }

      return records;
    } catch (error) {
      console.error('Error reading all records from Google Sheets:', error);
      throw new Error(`Failed to read from Google Sheets: ${error.message}`);
    }
  }
}
