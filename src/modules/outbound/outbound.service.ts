import { Injectable } from '@nestjs/common';
import { GoogleSheetsService } from './google-sheets.service';
import { CallApiService } from './call-api.service';
import { OutboundCallResult, OutboundRecord } from './interfaces/outbound.interface';
import { StartOutboundCallsDto } from './dto/outbound-call.dto';
import { OutboundEntityService } from './services/outbound-entity.service';
import { OutboundLogsService } from './services/outbound-logs.service';
import { CustomLoggerService } from '../../shared/custom-logger.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { callDestinations } from './contants/destinations';

@Injectable()
export class OutboundService {
  constructor(
    private readonly googleSheetsService: GoogleSheetsService,
    private readonly callApiService: CallApiService,
    private readonly loggerService: CustomLoggerService,
    private readonly outboundEntityService: OutboundEntityService,
    private readonly outboundLogsService: OutboundLogsService
  ) {}

  @Cron(CronExpression.EVERY_30_SECONDS, {
    waitForCompletion: true,
  }) // Evert second
  async handleCron() {
    this.loggerService.setContext(`CRON`);
    this.loggerService.log('===>>> CRON STARTING...');
    await this.syncExcelWithDatabase();
    await this.startOutboundCalls({ maxCalls: 3 });
    this.loggerService.log('<<<=== CRON END...');
  }

  async startOutboundCalls(dto: StartOutboundCallsDto): Promise<OutboundCallResult[]> {
    const sessionId = `outbound_${Date.now()}`;
    this.loggerService.setContext(sessionId);

    this.loggerService.log('Starting outbound calling session', dto);

    try {
      // Get new records from database instead of Google Sheets
      const dbRecords = await this.outboundEntityService.getNewRecords();
      this.loggerService.log(`Found ${dbRecords.length} records with 'new' status from database`);

      if (dbRecords.length === 0) {
        this.loggerService.log('No new records found to call');
        return [];
      }

      // Limit the number of calls if specified
      const recordsToCall = dto.maxCalls ? dbRecords.slice(0, dto.maxCalls) : dbRecords;

      this.loggerService.log(`Processing ${recordsToCall.length} calls`);

      const results: OutboundCallResult[] = [];

      for (const record of recordsToCall) {
        try {
          this.loggerService.log(
            `Processing call for ${record.number} (${record.name} ${record.surname})`
          );

          // Update status to 'calling' in Google Sheets and database
          await this.googleSheetsService.updateRecordStatus(record, 'calling');
          await this.outboundEntityService.updateCallInfo(record.id, '', 'calling');
          this.loggerService.log(`Updated status to 'calling' for ${record.number}`);
          let callResult;
          if (record.destination === 'mepas3') {
            callResult = await this.callApiService.makeVapiCall(
              record,
              dto.responseurl,
              callDestinations[record.destination]
            );
          } else {
            // Make the API call
            callResult = await this.callApiService.makeCall(
              record,
              dto.responseurl,
              callDestinations[record.destination]
            );
          }

          if (callResult.success) {
            // Log successful call
            await this.outboundLogsService.logCallInitiated(
              record.id,
              callResult.callId,
              dto.responseurl || '',
              { request: callResult?.requestData, record },
              callResult?.responseData
            );

            // Update status to 'completed' in Google Sheets and database
            await this.googleSheetsService.updateRecordStatus(record, 'completed');
            await this.outboundEntityService.updateCallInfo(
              record.id,
              callResult.callId,
              'completed'
            );

            this.loggerService.log(
              `Call successful for ${record.number}, callId: ${callResult.callId}`
            );

            results.push({
              record,
              success: true,
              callId: callResult.callId,
            });
          } else {
            await this.googleSheetsService.updateRecordStatus(record, 'failed');
            await this.outboundEntityService.updateCallInfo(record.id, '', 'failed');

            // Log failed call
            await this.outboundLogsService.logCallFailed(
              record.id,
              callResult.error || 'Unknown error',
              { record, responseurl: dto.responseurl },
              callResult
            );

            this.loggerService.error(`Call failed for ${record.number}`, {
              error: callResult.error,
            });

            results.push({
              record,
              success: false,
              error: callResult.error,
            });
          }

          await this.delay(1000); // 1 second delay
        } catch (error) {
          this.loggerService.error(`Error processing call for ${record.number}`, {
            error: error.message,
          });

          try {
            await this.googleSheetsService.updateRecordStatus(record, 'failed');
          } catch (updateError) {
            this.loggerService.error(`Failed to update status for ${record.number}`, {
              error: updateError.message,
            });
          }

          results.push({
            record,
            success: false,
            error: error.message,
          });
        }
      }

      this.loggerService.log('Outbound calling session completed', {
        totalProcessed: results.length,
        successful: results.filter((r) => r.success).length,
        failed: results.filter((r) => !r.success).length,
      });

      return results;
    } catch (error) {
      this.loggerService.error('Error in outbound calling session', {
        error: error.message,
      });
      throw error;
    }
  }

  async getOutboundRecords(): Promise<any> {
    try {
      // Get all records from database instead of Google Sheets
      return await this.outboundEntityService.findAll();
    } catch (error) {
      throw new Error(`Failed to get outbound records: ${error.message}`);
    }
  }

  async getNewRecords(): Promise<any> {
    return this.outboundEntityService.getNewRecords();
  }

  async testApiConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const isConnected = await this.callApiService.testConnection();
      return {
        success: isConnected,
        message: isConnected ? 'API connection successful' : 'API connection failed',
      };
    } catch (error) {
      return {
        success: false,
        message: `API connection test failed: ${error.message}`,
      };
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Sync Excel data with database
   */
  async syncExcelWithDatabase(): Promise<void> {
    try {
      this.loggerService.log('===>>> Starting Excel to database sync');

      // Get all records from Excel
      const excelRecords = await this.googleSheetsService.getAllRecords();
      this.loggerService.log(`Found ${excelRecords.length} records in Excel`);

      // Sync each record to database
      await this.outboundEntityService.syncFromExcel(excelRecords);

      this.loggerService.log('<<<=== Excel to database sync completed');
    } catch (error) {
      this.loggerService.error('Error syncing Excel with database', {
        error: error.message,
      });
      throw error;
    }
  }
}
