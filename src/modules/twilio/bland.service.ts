import { Injectable } from '@nestjs/common';
import { TwilioIncomingDto } from './dtos/twilio-incoming.dto';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { OutboundEntityService } from '../outbound/services/outbound-entity.service';
import { OutboundLogsService } from '../outbound/services/outbound-logs.service';
import { CustomLoggerService } from '../../shared/custom-logger.service';

@Injectable()
export class BlandService {
  protected url = process.env.BLAND_URL as string;

  constructor(
    private readonly httpService: HttpService,
    private readonly outboundEntityService: OutboundEntityService,
    private readonly outboundLogsService: OutboundLogsService,
    private readonly logger: CustomLoggerService
  ) {}

  async redirectToBland(twilioPayload: TwilioIncomingDto): Promise<any> {
    const number = twilioPayload.From;
    this.logger.setContext('Call' + twilioPayload.From);
    this.logger.log(`Redirecting call from ${number} to Bland`);

    try {
      // Look up outbound record by phone number
      const outboundRecord =
        await this.outboundEntityService.findByNumber(number);

      if (outboundRecord) {
        this.logger.log(
          `Found outbound record for ${number}: ${outboundRecord.name} ${outboundRecord.surname}`
        );

        // Log the Twilio redirection
        const callLog = await this.outboundLogsService.logTwilioRedirection(
          outboundRecord.id,
          twilioPayload,
        );

        // Call Bland with enhanced payload
        const blandResponse = await this.callBlandWithParams(
          twilioPayload,
          outboundRecord.name,
          outboundRecord.surname,
          outboundRecord.other
        );

        callLog.update({
          apiResponse : blandResponse
        })

        return  blandResponse;
      } else {
        this.logger.warn(
          `No outbound record found for ${number}, proceeding without additional parameters`
        );
        return this.callBland(twilioPayload);
      }
    } catch (error) {
      this.logger.error(
        `Error looking up outbound record for ${number}:`,
        error
      );
      // Fallback to original behavior if database lookup fails
      return this.callBland(twilioPayload);
    }
  }

  async callBland(payload: TwilioIncomingDto): Promise<any> {
    const { data } = await firstValueFrom(
      this.httpService.post<any>(this.url, payload).pipe(
        catchError((error: AxiosError) => {
          this.logger.error(error);
          throw 'An error happened!';
        })
      )
    );
    return data;
  }

  /**
   * Call Bland with additional parameters from outbound record
   */
  async callBlandWithParams(
    payload: TwilioIncomingDto,
    name: string,
    surname: string,
    other: any
  ): Promise<any> {
    const enhancedUrl = this.buildBlandUrlWithParams(name, surname, other);

    this.logger.log(`Calling Bland with enhanced URL: ${enhancedUrl}`);

    const { data } = await firstValueFrom(
      this.httpService.post<any>(enhancedUrl, payload).pipe(
        catchError((error: AxiosError) => {
          this.logger.error('Error calling Bland with parameters:', error);
          throw 'An error happened!';
        })
      )
    );
    return data;
  }

  /**
   * Build Bland URL with GET parameters
   */
  private buildBlandUrlWithParams(
    name: string,
    surname: string,
    other: Record<string, string>
  ): string {
    const url = new URL(this.url);
    url.searchParams.set('name', name);
    url.searchParams.set('surname', surname);

    Object.entries(other).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });

    return url.toString();
  }
}
