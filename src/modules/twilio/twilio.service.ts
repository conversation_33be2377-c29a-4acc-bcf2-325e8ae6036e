import { Injectable, Logger } from '@nestjs/common';
import { TwilioIncomingDto } from './dtos/twilio-incoming.dto';
import * as Twilio from 'twilio';

@Injectable()
export class TwilioService {
  private readonly logger = new Logger(TwilioService.name);

  handleTwilioIncoming(twilioPayload: TwilioIncomingDto): string {
    this.logger.log(
      `incoming call from Twilio: ${twilioPayload.From}, SID: ${twilioPayload.CallSid}`,
    );

    const twiml = new Twilio.twiml.VoiceResponse();

    twiml.say(
      { voice: 'alice', language: 'tr-TR' },
      '<PERSON><PERSON><PERSON><PERSON>, Rast Mobil sesli asistana hoş geldiniz.',
    );

    twiml.record({
      maxLength: 30,
      action: '/handle-recording', // Kayıttan sonra çağrılacak endpoint
      recordingStatusCallback: '/recording-status', // Kayıt tamamlandığında webhook
      recordingStatusCallbackEvent: ['completed']
    })

    twiml.hangup();

    return twiml.toString();
  }

  handleRecording(twilioPayload: any) {
    const recordingUrl = twilioPayload.RecordingUrl;
    this.logger.log('🎤 Kayıt tamamlandı: ', recordingUrl);

    const response = new Twilio.twiml.VoiceResponse();
    response.say('Kaydınız alınmıştır. Teşekkür ederiz.');
    return response.toString();
  }

  recordingStatus(twilioPayload: any) {
    this.logger.log('Recording status',twilioPayload);
  }
}
