import { Body, Controller, Post, Res } from '@nestjs/common';
import { TwilioService } from './twilio.service';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { TwilioIncomingDto } from './dtos/twilio-incoming.dto';
import { BlandService } from './bland.service';
import { ElevenHookDto } from './dtos/eleven-hook.dto';
import { UserHookService } from '../outbound/user-hook.service';

@ApiTags('twilio')
@Controller('twilio')
export class TwilioController {
  constructor(
    private readonly twilioService: TwilioService,
    private readonly blandService: BlandService,
    private readonly userHookService: UserHookService
  ) {}

  @Post('bland-gateway')
  async handleTwilioIncomingToBland(
    @Body() twilioIncomingDto: TwilioIncomingDto,
    @Res() res: Response
  ) {
    console.log('twilioIncomingDto', twilioIncomingDto);
    const twiml = await this.blandService.redirectToBland(twilioIncomingDto);

    console.log('twiml', twiml);
    // await this.delay(5000);
    res.header('Content-Type', 'application/xml');
    res.send(twiml);
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  @Post('incoming')
  handleTwilioIncoming(
    @Body() twilioIncomingDto: TwilioIncomingDto,
    @Res() res: Response
  ) {
    console.log(twilioIncomingDto);
    const twiml = this.twilioService.handleTwilioIncoming(twilioIncomingDto);
    res.header('Content-Type', 'application/xml');
    res.send(twiml);
  }

  @Post('call-status')
  handleRecording(@Body() dto: any, @Res() res: Response) {
    const response = this.twilioService.handleRecording(dto);
    res.header('Content-Type', 'application/xml');
    res.send(response);
  }

  @Post('user-hook')
  async userHook(@Body() elevenHookDto: ElevenHookDto) {
    console.log('elevenHookDto', elevenHookDto);

    const data = await this.userHookService.handleTwilioIncoming(elevenHookDto);
    console.log('dynamic_variables', data);

    return {
      dynamic_variables: data || {
        name: 'Tuncay',
        surname: 'Akdeniz',
        SubId: 'Üç Dört Beş',
        InvoiceCount: '2',
        Depth: 'Yüz Elli',
      },
    };
  }
}
