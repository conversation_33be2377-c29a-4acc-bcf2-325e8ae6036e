import { Module } from '@nestjs/common';
import { TwilioController } from './twilio.controller';
import { TwilioService } from './twilio.service';
import { BlandService } from './bland.service';
import { HttpModule } from '@nestjs/axios';
import { OutboundModule } from '../outbound/outbound.module';

@Module({
  imports: [HttpModule, OutboundModule],
  controllers: [TwilioController],
  providers: [TwilioService, BlandService],
})
export class TwilioModule {}
