import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { UserLoginRequestDto } from './dto/user-login-request.dto';
import { User } from '../users/user.entity';
import { JwtPayload } from './models/jwt-payload.model';
import { compare } from 'bcrypt';
import { randomInt } from 'crypto';
import { ConfigService } from '@nestjs/config';
import { UserLoginResponseDto } from './dto/user-login-response.dto';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  async validateUser(email: string, password: string): Promise<User> {
    const user = await this.usersService.getUserByEmail(email);
    if (!user) {
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Invalid email or password.',
          error: 'customError',
        },
        HttpStatus.BAD_REQUEST
      );
    }

    const isMatch = await compare(password, user.password);
    if (!isMatch) {
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Invalid email or password.',
          error: 'customError',
        },
        HttpStatus.BAD_REQUEST
      );
    }
    return user;
  }

  async signToken(user: User) {
    const payload: JwtPayload = {
      userId: user.id,
    };

    return this.jwtService.sign(payload, {
      expiresIn: this.configService.get('auth.expiresIn'),
    });
  }

  async login(userLoginRequestDto: UserLoginRequestDto) {
    const email = userLoginRequestDto.email;
    const password = userLoginRequestDto.password;

    const user = await this.validateUser(email, password);
    const token = await this.signToken(user);

    return new UserLoginResponseDto(user, token);
  }

  async generateResetCode(user: User): Promise<number> {
    const resetCode = randomInt(100000, 999999);
    await this.usersService.saveResetCode(user.email, resetCode);
    return resetCode;
  }
}
