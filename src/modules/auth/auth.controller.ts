import {
  Body,
  Controller,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { ApiOkResponse } from '@nestjs/swagger';
import { UserLoginResponseDto } from './dto/user-login-response.dto';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { UserLoginRequestDto } from './dto/user-login-request.dto';
import { UsersService } from '../users/users.service';
import { AuthService } from './auth.service';
import { MailerService } from '@nestjs-modules/mailer';
import { v4 as uuidv4 } from 'uuid';
import { ResetPasswordDto } from '../users/dto/reset-password.dto';
import { VerifyResetCodeDto } from '../users/dto/verify-code.dto';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly usersService: UsersService,
    private readonly authService: AuthService,
    private mailerService: MailerService
  ) {}

  @Post('register')
  @ApiOkResponse({ type: UserLoginResponseDto })
  register(
    @Body() createUserDto: CreateUserDto
  ): Promise<UserLoginResponseDto> {
    return this.usersService.create(createUserDto);
  }

  @Post('login')
  @HttpCode(200)
  @ApiOkResponse({ type: UserLoginResponseDto })
  login(
    @Body() userLoginRequestDto: UserLoginRequestDto
  ): Promise<UserLoginResponseDto> {
    return this.authService.login(userLoginRequestDto);
  }

  @Post('reset-mail')
  async sendMailResetPassword(@Body('email') email: string) {
    const user = await this.usersService.getUserByEmail(email);
    if (!user) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Bu e-posta adresi sistemimizde kayıtlı değil.',
          step: 0,
          error: 'customError',
        },
        HttpStatus.NOT_FOUND
      );
    }
    const resetCode = await this.authService.generateResetCode(user);
    await this.mailerService.sendMail({
      to: email,
      subject: 'Argesis Şifre Sıfırlama Talebi',
      template: './reset-password',
      context: {
        name: user.firstName,
        resetCode,
      },
    });
    console.log('test:', resetCode);
    return { message: 'Parola sıfırlama e-postası gönderildi.', step: 1 };
  }

  @Post('verify-reset-code')
  async verifyResetCode(
    @Body() verifyResetCodeDto: VerifyResetCodeDto
  ): Promise<any> {
    const { resetCode, email } = verifyResetCodeDto;
    const resetCodeEntry = await this.usersService.getResetCodeByCode(
      resetCode,
      email
    );
    if (!resetCodeEntry) {
      throw new HttpException(
        {
          step: 1,
          message: 'Geçersiz Güvenlik Kodu!',
          statusCode: HttpStatus.BAD_REQUEST,
          error: 'customError',
        },
        HttpStatus.BAD_REQUEST
      );
    }
    const setId = uuidv4();
    resetCodeEntry.setId = setId;
    await resetCodeEntry.save();
    return { step: 2, setId };
  }

  @Post('reset-password')
  async resetPassword(
    @Body('setId') setId: string,
    @Body() resetPasswordDto: ResetPasswordDto
  ) {
    const user = await this.usersService.getUserBySetId(setId);
    if (!user) {
      throw new HttpException(
        {
          step: 2,
          message: 'Güvenlik Kodu bulunamadı!',
          error: 'customError',
          statusCode: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST
      );
    }
    await this.usersService.resetPassword(setId, resetPasswordDto.password);
    return { step: 3, message: 'Parola sıfırlama işlemi başarılı' };
  }
}
