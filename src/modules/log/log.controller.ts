import { Controller, UseGuards } from '@nestjs/common';
import { ResourceController } from '../../core/base/resource.controller';
import { Log } from './log.entity';
import { LogDto } from './dto/log.dto';
import { LogService } from './log.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('logs')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('logs')
export class LogController extends ResourceController<Log, LogDto> {
  constructor(private readonly logService: LogService) {
    super(logService);
  }
}
