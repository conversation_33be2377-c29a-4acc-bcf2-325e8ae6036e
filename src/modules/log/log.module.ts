import { Modu<PERSON> } from '@nestjs/common';
import { LogController } from './log.controller';
import { LogService } from './log.service';
import { logProviders } from './log.providers';
import { DatabaseModule } from '../../core/database/database.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { LogInterceptor } from './log.interceptor';

@Module({
  imports: [DatabaseModule],
  controllers: [LogController],
  providers: [
    LogService,
    ...logProviders,
    {
      provide: APP_INTERCEPTOR,
      useClass: LogInterceptor,
    },
  ],
  exports: [LogService],
})
export class LogModule {}
