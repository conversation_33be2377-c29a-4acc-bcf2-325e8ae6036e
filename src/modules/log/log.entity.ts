import { Column, DataType, Table } from 'sequelize-typescript';
import { ResourceEntity } from '../../core/base/resource.entity';

@Table({
  tableName: 'logs',
})
export class Log extends ResourceEntity<Log> {
  @Column
  declare method: string;

  @Column
  declare path: string;

  @Column({ field: 'user_id', allowNull: true })
  declare userId: string;

  @Column({ field: 'ip_address' })
  declare ipAddress: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  declare body: object;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    field: 'query_params',
  })
  declare queryParams: object;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    field: 'user_info',
  })
  declare userInfo: object;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    field: 'response_data',
  })
  declare responseData: object;

  @Column({ field: 'status_code', allowNull: true })
  declare statusCode: number;

  @Column({ field: 'response_time', allowNull: true })
  declare responseTime: number;
}
