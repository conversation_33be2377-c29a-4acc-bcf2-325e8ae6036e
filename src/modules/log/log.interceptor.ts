import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { LogService } from './log.service';
import { CommonService } from '../../core/services/common.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LogInterceptor implements NestInterceptor {
  constructor(private readonly logService: LogService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    CommonService.requestId = uuidv4();

    // Skip logging for certain paths if needed
    if (request.path === '/logs') {
      return next.handle();
    }

    return next.handle().pipe(
      tap(() => {
        // Log the request after it's processed
        this.logService.logRequest(request, request.user);
      })
    );
  }
}
