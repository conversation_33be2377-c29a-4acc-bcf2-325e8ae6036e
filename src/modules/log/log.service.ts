import { Inject, Injectable } from '@nestjs/common';
import { BaseService } from '../../core/base/base.service';
import { Log } from './log.entity';
import { LogDto } from './dto/log.dto';
import { Request, Response } from 'express';

@Injectable()
export class LogService extends BaseService<Log, LogDto> {
  constructor(@Inject('LogRepository') repository: typeof Log) {
    super(repository);
  }

  /**
   * Log a request with user information
   */
  async logRequest(request: Request, user?: any): Promise<Log> {
    const logData: Partial<LogDto> = {
      method: request.method,
      path: request.path,
      ipAddress: this.getIpAddress(request),
      body: request.body,
      queryParams: request.query,
    };

    if (user) {
      logData.userId = user.id;
      logData.userInfo = {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      };
    }

    return this.repository.create(logData);
  }

  /**
   * Update log with response information
   */
  async updateLogWithResponse(log: Log, response: any, statusCode: number, responseTime: number): Promise<Log> {
    log.responseData = this.sanitizeResponseData(response);
    log.statusCode = statusCode;
    log.responseTime = responseTime;
    return log.save();
  }

  /**
   * Create a complete log entry with request and response data
   */
  async createCompleteLog(
    request: Request,
    response: any,
    statusCode: number,
    responseTime: number,
    user?: any
  ): Promise<Log> {
    const logData: Partial<LogDto> = {
      method: request.method,
      path: request.path,
      ipAddress: this.getIpAddress(request),
      body: request.body,
      queryParams: request.query,
      responseData: this.sanitizeResponseData(response),
      statusCode: statusCode,
      responseTime: responseTime
    };

    if (user) {
      logData.userId = user.id;
      logData.userInfo = {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      };
    }

    return this.repository.create(logData);
  }

  /**
   * Sanitize response data to prevent storing sensitive information
   * or excessively large responses
   */
  private sanitizeResponseData(data: any): any {
    // Clone the data to avoid modifying the original
    let sanitized = JSON.parse(JSON.stringify(data || {}));

    // Implement sanitization logic here if needed
    // For example, remove sensitive fields, truncate large responses, etc.

    return sanitized;
  }

  /**
   * Get the client IP address from the request
   */
  private getIpAddress(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}
