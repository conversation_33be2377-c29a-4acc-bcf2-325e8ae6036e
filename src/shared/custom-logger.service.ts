import { ConsoleLogger, Global, Injectable, LoggerService, LogLevel } from '@nestjs/common';

@Global()
@Injectable()
export class CustomLoggerService extends ConsoleLogger {
  protected formatMessage(
    logLevel: LogLevel,
    message: unknown,
    pidMessage: string,
    formattedLogLevel: string,
    contextMessage: string,
    timestampDiff: string,
  ) {
    const output = this.stringifyMessage(message, logLevel);
    formattedLogLevel = this.colorize(formattedLogLevel, logLevel);

    return `${this.getTimestamp()} ${formattedLogLevel} ${contextMessage}${output}${timestampDiff}\n`;
  }

  protected getTimestamp(): string {
    return new Date().toISOString().replace('T',' ')
  }

}
