import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { User } from '../../modules/users/user.entity';

@Injectable({
  scope: Scope.REQUEST,
})
export class CommonService {
  constructor(@Inject(REQUEST) private request: Request | any) {
    CommonService.requestId = request.id;
  }

  public static currentUser: User;
  public static requestId: string;
}
