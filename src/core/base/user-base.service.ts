import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { BaseServiceInterface } from '../interfaces/base-service.interface';
import { BaseEntity } from './base.entity';
import { BaseDto } from './base.dto';
import { CommonService } from '../services/common.service';

@Injectable()
export class UserBaseService<T extends BaseEntity<T>, D extends BaseDto>
  implements BaseServiceInterface<T, D>
{
  constructor(protected readonly repository: T | any) {}

  async getAll() {
    return this.repository.findAll({
      where: { userId: CommonService.currentUser.id },
    }) as T[];
  }

  async get(id: string | number) {
    const model = (await this.repository.findByPk(id, {
      where: { userId: CommonService.currentUser.id },
    })) as T;

    if (!model) {
      throw new HttpException(
        'Model with given id not found',
        HttpStatus.NOT_FOUND
      );
    }
    return model;
  }

  async create(model: Partial<D>) {
    const userId = CommonService.currentUser.id;
    return this.repository.create({ ...model, userId });
  }

  async update(id: string, model: D) {
    return await this.get(id).then((responseGet) => {
      responseGet.setAttributes(model as any);
      responseGet.save();
      return responseGet as T;
    });
  }

  async delete(id: string) {
    const user = await this.get(id);
    return user.destroy();
  }
}
