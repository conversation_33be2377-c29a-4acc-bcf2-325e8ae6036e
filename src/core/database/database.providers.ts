import { Sequelize } from 'sequelize-typescript';
import { User } from '../../modules/users/user.entity';
import { ConfigService } from '@nestjs/config';
import { Asset } from '../../modules/asset/asset.entity';
import { Outbound } from '../../modules/outbound/entities/outbound.entity';
import { OutboundLogs } from '../../modules/outbound/entities/outbound-logs.entity';
import { Log } from '../../modules/log/log.entity';

export const databaseProviders = [
  {
    provide: 'SEQUELIZE',
    useFactory: async (configService: ConfigService) => {
      const sequelize = new Sequelize(configService.get('database'));
      sequelize.addModels([User, Asset, Outbound, OutboundLogs, Log]);
      await sequelize.sync();
      return sequelize;
    },
    inject: [ConfigService],
  },
];
