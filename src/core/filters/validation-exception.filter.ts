import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { ValidationException } from '../exceptions/validation.exception';

@Catch(ValidationException)
export class ValidationExceptionFilter implements ExceptionFilter {
  catch(exception: ValidationException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = HttpStatus.UNPROCESSABLE_ENTITY;

    const result: any = exception.getResponse();

    result.errors = result.message;
    result.message = result.message.reduce(
      (acc, item) => acc.concat(Object.values(item.constraints)),
      []
    );
    result.statusCode = status;

    if (process.env.DEBUG_MODE != '0') {
      // TODO read config module, and type check
      result.exception = exception;
    }

    response.status(status).json(result);
  }
}
