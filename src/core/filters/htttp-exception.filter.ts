import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { UniqueConstraintError } from 'sequelize';

@Catch(UniqueConstraintError)
export class UniqueConstraintErrorFilter implements ExceptionFilter {
  catch(exception: UniqueConstraintError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = HttpStatus.CONFLICT;

    const result: any = {
      statusCode: status,
      message: exception.errors.map((e) => e.message),
      error: 'CONFLICT',
    };

    if (process.env.DEBUG_MODE != '0') {
      // TODO read config module, and type check
      result.exception = exception;
    }

    response.status(status).json(result);
  }
}
