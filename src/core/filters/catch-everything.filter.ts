import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';

@Catch()
export class CatchEverythingFilter implements ExceptionFilter {
  catch(exception: TypeError | HttpException | Error, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const responseBody: any = {
      statusCode: httpStatus,
      message:
        exception instanceof HttpException
          ? exception.message
          : 'Internal Server Error',
      timestamp: new Date().toISOString(),
      path: ctx.getRequest().url,
    };

    if (process.env.DEBUG_MODE != '0') {
      responseBody.exception = exception?.toString();
      responseBody.stack = exception?.stack;
    }
    console.error(exception);

    response.status(httpStatus).json(responseBody);
  }
}
