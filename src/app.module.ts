import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import databaseConfig from '../config/database.config';
import appConfig from '../config/app.config';
import { CallModule } from './modules/call/call.module';
import { AssetModule } from './modules/asset/asset.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { RealtimeModule } from './modules/realtime/realtime.module';
import { TwilioModule } from './modules/twilio/twilio.module';
import { OutboundModule } from './modules/outbound/outbound.module';
import { LogModule } from './modules/log/log.module';
import { ScheduleModule } from '@nestjs/schedule';
import { SharedModule } from './shared/shared.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig, databaseConfig],
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'uploads'),
      serveRoot: '/uploads',
    }),
    // UsersModule,
    CallModule,
    AssetModule,
    RealtimeModule,
    TwilioModule,
    OutboundModule,
    LogModule,
    SharedModule,
    ScheduleModule.forRoot()
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
